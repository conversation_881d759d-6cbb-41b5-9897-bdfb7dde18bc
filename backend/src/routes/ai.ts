import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { authenticateToken } from '@/middleware/auth';
import { aiService } from '@/services/ai';
import { logger } from '@/utils/logger';
import { User } from '@/models/User';
import { ClothingItem } from '@/models/ClothingItem';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

/**
 * POST /api/ai/fashion-advice
 * Get AI fashion advice based on user query and context
 */
router.post('/fashion-advice',
  authenticateToken,
  [
    body('query')
      .notEmpty()
      .withMessage('Query is required')
      .isLength({ min: 3, max: 500 })
      .withMessage('Query must be between 3 and 500 characters'),
    body('includeUserProfile')
      .optional()
      .isBoolean()
      .withMessage('includeUserProfile must be a boolean'),
    body('context.season')
      .optional()
      .isIn(['dry', 'wet', 'cool', 'hot'])
      .withMessage('Season must be one of: dry, wet, cool, hot'),
    body('context.occasion')
      .optional()
      .isString()
      .withMessage('Occasion must be a string'),
    body('context.budget')
      .optional()
      .isNumeric()
      .withMessage('Budget must be a number'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { query, includeUserProfile = true, context = {} } = req.body;

      let userProfile;
      if (includeUserProfile) {
        const user = await User.findById(userId).select('profile preferences');
        if (user) {
          userProfile = {
            preferences: user.preferences?.styles || [],
            size: user.profile?.size || '',
            style: user.preferences?.preferredStyle || '',
          };
        }
      }

      // Get user's available items for context
      const availableItems = await ClothingItem.find({
        owner: userId,
        status: 'available'
      }).select('title category condition size color brand estimatedValue').limit(20);

      const fashionAdviceRequest = {
        userProfile,
        query,
        context: {
          ...context,
          availableItems: availableItems.map(item => ({
            title: item.title,
            category: item.category,
            condition: item.condition,
            size: item.size,
            color: item.color,
            brand: item.brand,
            estimatedValue: item.estimatedValue
          })),
          userLocation: 'Kenya', // Could be made dynamic based on user location
        }
      };

      const advice = await aiService.getFashionAdvice(fashionAdviceRequest);

      // Log the interaction for analytics
      logger.info('AI fashion advice requested', {
        userId,
        query: query.substring(0, 50) + '...',
        success: advice.success
      });

      res.json({
        success: true,
        data: advice
      });

    } catch (error: any) {
      logger.error('Failed to get AI fashion advice:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get fashion advice',
        error: error.message
      });
    }
  }
);

/**
 * POST /api/ai/outfit-suggestions
 * Get AI outfit suggestions based on user's wardrobe
 */
router.post('/outfit-suggestions',
  authenticateToken,
  [
    body('occasion')
      .optional()
      .isString()
      .withMessage('Occasion must be a string'),
    body('weather')
      .optional()
      .isIn(['hot', 'warm', 'cool', 'cold', 'rainy'])
      .withMessage('Weather must be one of: hot, warm, cool, cold, rainy'),
    body('itemIds')
      .optional()
      .isArray()
      .withMessage('itemIds must be an array'),
    body('itemIds.*')
      .optional()
      .isMongoId()
      .withMessage('Each itemId must be a valid MongoDB ObjectId'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { occasion = 'casual', weather = 'warm', itemIds } = req.body;

      // Get user's wardrobe items
      let wardrobeQuery: any = {
        owner: userId,
        status: 'available'
      };

      if (itemIds && itemIds.length > 0) {
        wardrobeQuery._id = { $in: itemIds };
      }

      const wardrobeItems = await ClothingItem.find(wardrobeQuery)
        .select('title category condition size color brand estimatedValue images')
        .limit(50);

      if (wardrobeItems.length === 0) {
        return res.json({
          success: true,
          data: {
            success: false,
            advice: 'You don\'t have any available items in your wardrobe yet. Start by adding some clothing items to get personalized outfit suggestions!',
            recommendations: {
              tips: ['Add clothing items to your wardrobe', 'Upload clear photos of your clothes', 'Set accurate categories and conditions']
            }
          }
        });
      }

      const query = `Create outfit suggestions for a ${occasion} occasion in ${weather} weather using items from my wardrobe. Focus on creating versatile, stylish combinations that promote sustainable fashion practices.`;

      const fashionAdviceRequest = {
        query,
        context: {
          availableItems: wardrobeItems.map(item => ({
            id: item._id,
            title: item.title,
            category: item.category,
            condition: item.condition,
            size: item.size,
            color: item.color,
            brand: item.brand,
            estimatedValue: item.estimatedValue
          })),
          occasion,
          weather,
          userLocation: 'Kenya'
        }
      };

      const advice = await aiService.getFashionAdvice(fashionAdviceRequest);

      // Enhance the response with actual item references
      if (advice.success && advice.recommendations) {
        advice.recommendations.items = wardrobeItems.map(item => ({
          _id: item._id,
          title: item.title,
          category: item.category,
          color: item.color,
          images: item.images
        }));
      }

      logger.info('AI outfit suggestions requested', {
        userId,
        occasion,
        weather,
        itemCount: wardrobeItems.length,
        success: advice.success
      });

      res.json({
        success: true,
        data: advice
      });

    } catch (error: any) {
      logger.error('Failed to get outfit suggestions:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get outfit suggestions',
        error: error.message
      });
    }
  }
);

/**
 * POST /api/ai/wardrobe-analysis
 * Get AI analysis of user's wardrobe with sustainability insights
 */
router.post('/wardrobe-analysis',
  authenticateToken,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;

      // Get comprehensive wardrobe data
      const wardrobeItems = await ClothingItem.find({
        owner: userId
      }).select('title category condition size color brand estimatedValue originalPrice createdAt');

      if (wardrobeItems.length === 0) {
        return res.json({
          success: true,
          data: {
            success: false,
            advice: 'Your wardrobe is empty! Start building your sustainable wardrobe by adding clothing items.',
            recommendations: {
              tips: [
                'Add items you already own to track your wardrobe',
                'Focus on quality over quantity',
                'Consider the versatility of each piece',
                'Look for gaps in your wardrobe that could be filled through swaps or sustainable purchases'
              ],
              sustainabilityScore: 0
            }
          }
        });
      }

      // Calculate wardrobe statistics
      const stats = {
        totalItems: wardrobeItems.length,
        categories: [...new Set(wardrobeItems.map(item => item.category))],
        conditions: wardrobeItems.reduce((acc: any, item) => {
          acc[item.condition] = (acc[item.condition] || 0) + 1;
          return acc;
        }, {}),
        totalValue: wardrobeItems.reduce((sum, item) => sum + (item.estimatedValue || item.originalPrice || 0), 0),
        averageValue: 0
      };
      stats.averageValue = stats.totalValue / stats.totalItems;

      const query = `Analyze my wardrobe and provide sustainability insights. I have ${stats.totalItems} items across categories: ${stats.categories.join(', ')}. Total estimated value: KES ${stats.totalValue.toLocaleString()}. Provide recommendations for building a more sustainable, versatile wardrobe.`;

      const fashionAdviceRequest = {
        query,
        context: {
          availableItems: wardrobeItems.map(item => ({
            title: item.title,
            category: item.category,
            condition: item.condition,
            estimatedValue: item.estimatedValue || item.originalPrice
          })),
          wardrobeStats: stats,
          userLocation: 'Kenya'
        }
      };

      const advice = await aiService.getFashionAdvice(fashionAdviceRequest);

      // Enhance response with wardrobe statistics
      if (advice.success && advice.recommendations) {
        advice.recommendations.wardrobeStats = stats;
        advice.recommendations.insights = {
          mostCommonCategory: stats.categories.reduce((a, b) => 
            wardrobeItems.filter(item => item.category === a).length > 
            wardrobeItems.filter(item => item.category === b).length ? a : b
          ),
          conditionBreakdown: stats.conditions,
          sustainabilityTips: [
            'Focus on quality pieces that last longer',
            'Consider swapping items you rarely wear',
            'Donate items in good condition to extend their lifecycle',
            'Look for versatile pieces that work in multiple outfits'
          ]
        };
      }

      logger.info('AI wardrobe analysis requested', {
        userId,
        itemCount: wardrobeItems.length,
        totalValue: stats.totalValue,
        success: advice.success
      });

      res.json({
        success: true,
        data: advice
      });

    } catch (error: any) {
      logger.error('Failed to analyze wardrobe:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to analyze wardrobe',
        error: error.message
      });
    }
  }
);

/**
 * POST /api/ai/styling-tips
 * Get AI styling tips for specific items or categories
 */
router.post('/styling-tips',
  authenticateToken,
  [
    body('itemId')
      .optional()
      .isMongoId()
      .withMessage('itemId must be a valid MongoDB ObjectId'),
    body('category')
      .optional()
      .isString()
      .withMessage('category must be a string'),
    body('focus')
      .optional()
      .isIn(['versatility', 'sustainability', 'trends', 'care', 'mixing'])
      .withMessage('focus must be one of: versatility, sustainability, trends, care, mixing'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { itemId, category, focus = 'versatility' } = req.body;

      let query = '';
      let contextItems = [];

      if (itemId) {
        // Get specific item styling tips
        const item = await ClothingItem.findOne({
          _id: itemId,
          owner: userId
        }).select('title category condition size color brand estimatedValue');

        if (!item) {
          return res.status(404).json({
            success: false,
            message: 'Item not found'
          });
        }

        query = `Provide styling tips for my ${item.category} item: ${item.title} (${item.color}, ${item.condition} condition). Focus on ${focus} and sustainable fashion practices.`;
        contextItems = [item];
      } else if (category) {
        // Get category-specific styling tips
        const categoryItems = await ClothingItem.find({
          owner: userId,
          category: category,
          status: 'available'
        }).select('title category condition size color brand estimatedValue').limit(10);

        query = `Provide styling tips for ${category} items in my wardrobe. Focus on ${focus} and how to create versatile, sustainable outfits.`;
        contextItems = categoryItems;
      } else {
        // General styling tips
        query = `Provide general styling tips focused on ${focus} and sustainable fashion practices for someone living in Kenya.`;
      }

      const fashionAdviceRequest = {
        query,
        context: {
          availableItems: contextItems.map(item => ({
            title: item.title,
            category: item.category,
            condition: item.condition,
            color: item.color,
            brand: item.brand
          })),
          focus,
          userLocation: 'Kenya'
        }
      };

      const advice = await aiService.getFashionAdvice(fashionAdviceRequest);

      logger.info('AI styling tips requested', {
        userId,
        itemId,
        category,
        focus,
        success: advice.success
      });

      res.json({
        success: true,
        data: advice
      });

    } catch (error: any) {
      logger.error('Failed to get styling tips:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get styling tips',
        error: error.message
      });
    }
  }
);

/**
 * POST /api/ai/generate-description
 * Generate AI description for clothing items
 */
router.post('/generate-description',
  authenticateToken,
  [
    body('itemDetails')
      .notEmpty()
      .withMessage('itemDetails is required'),
    body('itemDetails.title')
      .notEmpty()
      .withMessage('Item title is required'),
    body('itemDetails.category')
      .notEmpty()
      .withMessage('Item category is required'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const { itemDetails } = req.body;

      const description = await aiService.generateItemDescription(itemDetails);

      logger.info('AI item description generated', {
        userId: req.user!.id,
        itemTitle: itemDetails.title,
        category: itemDetails.category
      });

      res.json({
        success: true,
        data: {
          description
        }
      });

    } catch (error: any) {
      logger.error('Failed to generate item description:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate description',
        error: error.message
      });
    }
  }
);

export default router;
